import React from 'react';
import { useMessageGetter } from '@messageformat/react';
import type {
  PatchApiProjectsProjectIdControlCenterPotentialChangesPotentialChangeIdMutationRequestSchema,
  PotentialChangeDetailsBasicSchema,
} from '@shape-construction/api/src/types';
import InputNumber from '@shape-construction/arch-ui/src/InputNumber';

type WorkingDaysOfDelayProps = {
  record: PotentialChangeDetailsBasicSchema;
  onUpdatePotentialChangeRecord: (
    values: PatchApiProjectsProjectIdControlCenterPotentialChangesPotentialChangeIdMutationRequestSchema
  ) => void;
};

export const WorkingDaysOfDelay: React.FC<WorkingDaysOfDelayProps> = ({
  record,
  onUpdatePotentialChangeRecord,
  ...props
}) => {
  const messages = useMessageGetter('controlCenter.commercialTracker.fields');
  const [workingDays, setWorkingDays] = React.useState(record.workingDaysOfDelay?.toString() || '');

  const handleBlur = (e: React.FocusEvent<HTMLInputElement>) => {
    const trimmedValue = e.target.value.trim();
    const numValue = trimmedValue ? Number.parseFloat(trimmedValue) : undefined;
    if (numValue !== record.workingDaysOfDelay) {
      onUpdatePotentialChangeRecord({ working_days_of_delay: numValue });
    }
  };

  return (
    <InputNumber
      {...props}
      aria-label={messages('workingDaysOfDelay')}
      name="workingDaysOfDelay"
      placeholder="0"
      step="0.1"
      onChange={(e: React.ChangeEvent<HTMLInputElement>) => setWorkingDays(e.target.value)}
      value={workingDays}
      fullWidth
      className="bg-transparent !text-xs leading-4 font-normal text-neutral-bold border-none shadow-none
        truncate px-2.5 py-1 rounded-sm
        focus:ring-2 hover:ring-1 hover:ring-gray-400 hover:text-gray-700 hover:bg-gray-50"
      onBlur={handleBlur}
    />
  );
};
