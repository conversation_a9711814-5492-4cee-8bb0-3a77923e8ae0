import React from 'react';
import { potentialChangeFactory } from '@shape-construction/api/factories/control-center';
import { render, screen, userEvent } from 'tests/test-utils';
import { PotentialChangeRow } from './PotentialChangeRow';

describe('<PotentialChangeRow />', () => {
  const potentialChange = potentialChangeFactory();
  const mockOnViewChangeSignals = jest.fn();

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('when linked signals is clicked', () => {
    it('calls onViewChangeSignals', async () => {
      render(
        <PotentialChangeRow
          setPotentialChangesToLink={() => {}}
          potentialChangesToLink={[]}
          record={potentialChange}
          projectId={'propject-01'}
          onViewChangeSignals={mockOnViewChangeSignals}
        />
      );

      await userEvent.click(screen.getByLabelText('controlCenter.commercialTracker.fields.linkedSignals'));

      expect(mockOnViewChangeSignals).toHaveBeenCalledWith(potentialChange);
    });
  });

  describe('when isSelectionMode is true', () => {
    it('renders the selection checkbox', () => {
      render(
        <PotentialChangeRow
          setPotentialChangesToLink={() => {}}
          potentialChangesToLink={[]}
          record={potentialChange}
          projectId={'propject-01'}
          onViewChangeSignals={mockOnViewChangeSignals}
          isSelectionMode={true}
        />
      );

      expect(
        screen.getByRole('checkbox', { name: 'controlCenter.commercialTracker.fields.selectPotentialChange' })
      ).toBeInTheDocument();
    });
  });

  describe('when checkboxes are clicked', () => {
    it('calls setPotentialChangesToLink with the correct value', async () => {
      const setPotentialChangesToLink = jest.fn();
      render(
        <PotentialChangeRow
          setPotentialChangesToLink={setPotentialChangesToLink}
          potentialChangesToLink={[]}
          record={potentialChange}
          projectId={'propject-01'}
          onViewChangeSignals={mockOnViewChangeSignals}
          isSelectionMode={true}
        />
      );

      await userEvent.click(
        screen.getByRole('checkbox', { name: 'controlCenter.commercialTracker.fields.selectPotentialChange' })
      );
      const updateFunction = setPotentialChangesToLink.mock.calls[0][0];

      expect(updateFunction([])).toEqual([potentialChange.id]);
    });
  });

  describe('when rendering all table cells', () => {
    it('renders all column components', () => {
      const testRecord = potentialChangeFactory({
        title: 'Test Title',
        cost: '$1,000',
        workingDaysOfDelay: 5.5,
        comment: 'Test comment',
      });

      render(
        <PotentialChangeRow
          setPotentialChangesToLink={() => {}}
          potentialChangesToLink={[]}
          record={testRecord}
          projectId={'project-01'}
          onViewChangeSignals={mockOnViewChangeSignals}
        />
      );

      // Verify all editable input components with values
      expect(screen.getByDisplayValue('Test Title')).toBeInTheDocument(); // Title
      expect(screen.getByDisplayValue('$1,000')).toBeInTheDocument(); // Cost
      expect(screen.getByDisplayValue('5.5')).toBeInTheDocument(); // WorkingDaysOfDelay
      expect(screen.getByDisplayValue('Test comment')).toBeInTheDocument(); // Comments

      // Verify dropdown/select components are present
      expect(screen.getByRole('button', { name: /priority/i })).toBeInTheDocument();
      expect(screen.getByRole('button', { name: /category/i })).toBeInTheDocument();
      expect(screen.getByRole('button', { name: /status/i })).toBeInTheDocument();
      expect(screen.getByRole('button', { name: /estimatedCostImpact/i })).toBeInTheDocument();
      expect(screen.getByRole('button', { name: /estimatedScheduleImpact/i })).toBeInTheDocument();
      expect(screen.getByRole('button', { name: /earlyWarningNoticeSubmitted/i })).toBeInTheDocument();

      // Verify read-only display components are present
      expect(screen.getByLabelText(/author/i)).toBeInTheDocument();
      expect(screen.getByLabelText(/createdAt/i)).toBeInTheDocument();
      expect(screen.getByRole('button', { name: /linkSignals/i })).toBeInTheDocument();

      // Verify action components are present
      expect(screen.getByRole('button', { name: /archive/i })).toBeInTheDocument();
    });
  });
});
