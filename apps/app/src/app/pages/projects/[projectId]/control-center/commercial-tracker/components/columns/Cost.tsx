import React from 'react';
import { useMessageGetter } from '@messageformat/react';
import type {
  PatchApiProjectsProjectIdControlCenterPotentialChangesPotentialChangeIdMutationRequestSchema,
  PotentialChangeDetailsBasicSchema,
} from '@shape-construction/api/src/types';
import InputText from '@shape-construction/arch-ui/src/InputText';

type CostProps = {
  record: PotentialChangeDetailsBasicSchema;
  onUpdatePotentialChangeRecord: (
    values: PatchApiProjectsProjectIdControlCenterPotentialChangesPotentialChangeIdMutationRequestSchema
  ) => void;
};

export const Cost: React.FC<CostProps> = ({ record, onUpdatePotentialChangeRecord, ...props }) => {
  const messages = useMessageGetter('controlCenter.commercialTracker.fields');
  const [cost, setCost] = React.useState(record.cost || '');

  const handleBlur = (e: React.FocusEvent<HTMLInputElement>) => {
    const trimmedValue = e.target.value.trim();
    if (trimmedValue !== record.cost) {
      onUpdatePotentialChangeRecord({ cost: trimmedValue });
    }
  };

  return (
    <InputText
      {...props}
      aria-label={messages('cost')}
      name="cost"
      placeholder="0"
      onChange={(e: React.ChangeEvent<HTMLInputElement>) => setCost(e.target.value)}
      value={cost}
      fullWidth
      className="bg-transparent !text-xs leading-4 font-normal text-neutral-bold border-none shadow-none
        px-2.5 py-1 rounded-sm
        focus:ring-2 hover:ring-1 hover:ring-gray-400 hover:text-gray-700 hover:bg-gray-50"
      onBlur={handleBlur}
    />
  );
};
