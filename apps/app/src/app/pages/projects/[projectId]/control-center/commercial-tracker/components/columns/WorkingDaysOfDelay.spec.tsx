import React from 'react';
import { potentialChangeFactory } from '@shape-construction/api/factories/control-center';
import { render, screen, userEvent } from 'tests/test-utils';
import { WorkingDaysOfDelay } from './WorkingDaysOfDelay';

describe('<WorkingDaysOfDelay />', () => {
  const mockOnUpdatePotentialChangeRecord = jest.fn();
  const potentialChange = potentialChangeFactory();

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('renders with initial working days value', () => {
    render(
      <WorkingDaysOfDelay
        record={{ ...potentialChange, workingDaysOfDelay: 5.5 }}
        onUpdatePotentialChangeRecord={mockOnUpdatePotentialChangeRecord}
      />
    );

    expect(screen.getByRole('spinbutton')).toHaveValue(5.5);
  });

  it('calls onUpdatePotentialChangeRecord on blur with updated value', async () => {
    render(
      <WorkingDaysOfDelay record={potentialChange} onUpdatePotentialChangeRecord={mockOnUpdatePotentialChangeRecord} />
    );

    await userEvent.clear(screen.getByRole('spinbutton'));
    await userEvent.type(screen.getByRole('spinbutton'), '10.25');
    await userEvent.tab();

    expect(mockOnUpdatePotentialChangeRecord).toHaveBeenCalledWith({ working_days_of_delay: 10.25 });
  });

  it('handles empty initial working days', () => {
    render(
      <WorkingDaysOfDelay
        record={{ ...potentialChange, workingDaysOfDelay: null }}
        onUpdatePotentialChangeRecord={mockOnUpdatePotentialChangeRecord}
      />
    );

    expect(screen.getByRole('spinbutton')).toHaveValue(null);
  });

  it('handles zero value', async () => {
    render(
      <WorkingDaysOfDelay record={potentialChange} onUpdatePotentialChangeRecord={mockOnUpdatePotentialChangeRecord} />
    );

    await userEvent.clear(screen.getByRole('spinbutton'));
    await userEvent.type(screen.getByRole('spinbutton'), '0');
    await userEvent.tab();

    expect(mockOnUpdatePotentialChangeRecord).toHaveBeenCalledWith({ working_days_of_delay: 0 });
  });

  it('handles empty value as undefined', async () => {
    render(
      <WorkingDaysOfDelay record={potentialChange} onUpdatePotentialChangeRecord={mockOnUpdatePotentialChangeRecord} />
    );

    await userEvent.clear(screen.getByRole('spinbutton'));
    await userEvent.tab();

    expect(mockOnUpdatePotentialChangeRecord).not.toHaveBeenCalled();
  });

  it('trims whitespace before parsing', async () => {
    render(
      <WorkingDaysOfDelay record={potentialChange} onUpdatePotentialChangeRecord={mockOnUpdatePotentialChangeRecord} />
    );

    await userEvent.clear(screen.getByRole('spinbutton'));
    await userEvent.type(screen.getByRole('spinbutton'), '  7.75  ');
    await userEvent.tab();

    expect(mockOnUpdatePotentialChangeRecord).toHaveBeenCalledWith({ working_days_of_delay: 7.75 });
  });

  it('does not call onUpdatePotentialChangeRecord when value is unchanged', async () => {
    render(
      <WorkingDaysOfDelay
        record={{ ...potentialChange, workingDaysOfDelay: 5.5 }}
        onUpdatePotentialChangeRecord={mockOnUpdatePotentialChangeRecord}
      />
    );

    await userEvent.click(screen.getByRole('spinbutton'));
    await userEvent.tab();

    expect(mockOnUpdatePotentialChangeRecord).not.toHaveBeenCalled();
  });

  it('handles decimal values correctly', async () => {
    render(
      <WorkingDaysOfDelay record={potentialChange} onUpdatePotentialChangeRecord={mockOnUpdatePotentialChangeRecord} />
    );

    await userEvent.clear(screen.getByRole('spinbutton'));
    await userEvent.type(screen.getByRole('spinbutton'), '3.14');
    await userEvent.tab();

    expect(mockOnUpdatePotentialChangeRecord).toHaveBeenCalledWith({ working_days_of_delay: 3.14 });
  });
});
